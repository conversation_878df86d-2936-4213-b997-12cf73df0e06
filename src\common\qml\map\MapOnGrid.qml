import QtQuick 
import QtQuick.Controls 
import QtQuick.Layouts
import QtLocation
import QtPositioning
import models 1.0

Rectangle {
    id: mapOnGrid

    property bool isLongPress: false
    anchors.fill: parent
    property var cameraList: []
    property int previewWidth: 500
    property int previewHeight: 340
    property var thisMapModel: mapModel ? mapModel : null
    property var thisMapState: mapState ? mapState : null

    property string prevItem: ""
    property string curItem: "1"

    property var currentItem: null
    property bool previewItemHovering: false
    property bool currentItemHovering: false

    signal previewingItemChanged()

    onCurItemChanged: {
        if (prevItem !== curItem && curItem !== "") {
            prevItem = curItem
            previewingItemChanged()
        }
    }

    onPreviewingItemChanged: {
        openPreviewTimer.stop()
        if (curItem !== "") {
            openPreviewTimer.start()
        }
    }

    property var mainGrid: {
        if(thisMapState && thisMapState.editMode) return null;
        var current = mapOnGrid.parent
        while (current) {
            if (current.objectName === "MainGrid" || current.toString().indexOf("MainGrid") !== -1) {
                return current
            }
            current = current.parent
        }
        return null
    }

    Loader {
        id: cameraDetailLoader
        width: previewWidth
        height: previewHeight
        visible: sourceComponent !== null
        active: thisMapState ? thisMapState.editMode : false
        z: 1000
        parent: mainGrid ? mainGrid : mapOnGrid
    }
    // Plugin {
    //     id: mapPlugin
    //     name: "osm"
    //     PluginParameter {
    //         name: "osm.mapping.providersrepository.disabled"
    //         value: "true"
    //     }
    //     PluginParameter {
    //         name: "osm.cache.directory"
    //         value: ""
    //     }
    //     PluginParameter {
    //         name: "osm.cache.disk.cost"
    //         value: "0"
    //     }
    //     PluginParameter {
    //         name: "osm.cache.memory.cost"
    //         value: "0"
    //     }
    // }

    // ConfirmDialog {
    //     id: createDialog
    //     z: 9999
    //     onCreateBuilding: {
    //         console.log("Creating building:", buildingName)
    //         // Gọi controller xử lý tạo building ở đây
    //         createDialog.close()
    //     }
    //     onCancel: createDialog.close()
    // }
    
    DropArea {
        anchors.fill: parent
        enabled: thisMapState ? thisMapState.editMode && !thisMapState.lockMode : false // chỉ nhận drop ở chế độ chỉnh sửa map và trạng thái là unlock
        onDropped: function(drop) {
            if (thisMapState.editMode) {
                let coord = map.toCoordinate(Qt.point(drop.x, drop.y))
                thisMapModel.handleDrop(drop.getDataAsArrayBuffer("application/json"),coord.latitude,coord.longitude)
            } else {
                thisMapState.notifyChanged(thisMapState.SelectEditMode)
            }

        }
    }
    
    Map {
        id: map
        anchors.fill: parent
        plugin: Plugin {
            id: mapPlugin
            name: "osm"
            PluginParameter {
                name: "osm.mapping.custom.host"
                value: "https://api.gpstech.vn/geo-service/styles/basic-preview/%z/%x/%y.png?access_token=" + (thisMapModel.accessToken ? thisMapModel.accessToken : "")
            }
            // PluginParameter {
            //     name: "osm.mapping.offline.directory"
            //     value: "D:/GEOSERVER"
            // }
            // PluginParameter {
            //     name: "osm.mapping.cache.directory"
            //     value: "D:/GEOSERVER"
            // }
            PluginParameter {
                name: "osm.cache.directory"
                value: ""
            }
            PluginParameter {
                name: "osm.cache.disk.cost"
                value: "0"
            }
            PluginParameter {
                name: "osm.cache.memory.cost"
                value: "0"
            }
        }

        zoomLevel: 14
        center: QtPositioning.coordinate(21.014506, 105.846509)
        activeMapType: supportedMapTypes[supportedMapTypes.length - 1]
        property geoCoordinate startCentroid
        antialiasing: true

        // Function to handle map movement
        function handleMapMove(dx, dy) {
            if (mapOnGrid.isMapMoveEnabled) {
                // Pan the map in the opposite direction of mouse movement
                // This creates a natural dragging effect
                map.pan(-dx, -dy)
            }
        }

        MapItemView {
            id: mapItemView
            model: (function(){
                    return thisMapModel.cameraIds
                })()
            delegate: MapItem {
                id: cameraItem
                model: (function(){
                    return modelData
                })()
                coordinate: QtPositioning.coordinate(modelData.latitude, modelData.longitude)
                rtsp: modelData.urlMainstream
                itemId: modelData.id
                itemName: modelData.name
                camLocation: modelData.location
                onUntrackSignal: (cameraModel) => {
                    thisMapModel.removeCameraFromMap(cameraModel)
                }
                onButtonSignal: () => {
                    if (thisMapState.editMode){
                        handleShowDialogEditMode(cameraItem, previewItemComponent)
                    }
                }
                Component {
                    id: previewItemComponent
                    PreviewItem {
                        id: previewItem
                        model: cameraItem.model
                        buttonType: "Camera"
                        isViewMode: thisMapState ? !thisMapState.editMode : false
                        isPlayingStream: true
                        visible: true
                        itemName: cameraItem.itemName
                        onCloseSignal: {
                            cameraDetailLoader.sourceComponent = null;
                            cameraDetailLoader.width = previewWidth
                            cameraDetailLoader.height = previewHeight
                            thisMapState.viewMode = false
                        }
                        onFullScreenSignal: {
                            if (thisMapState.editMode){
                                handleDialogPositionEditMode(cameraItem)
                            }
                        }
                        onHoverStateChanged: function (isHovering) {
                            previewItemHovering = isHovering
                            if(!isHovering){
                                hoverTimer.start()
                            }
                            else hoverTimer.stop()
                        }
                    }
                }

                onIsItemHoveredChanged: {
                    if(!thisMapState.editMode){
                        openPreviewTimer.stop()
                        hoverTimer.stop()

                        if (isItemHovered) {
                            handlePositionDialogViewMode(cameraItem, previewItemComponent)
                            curItem = cameraItem.itemId
                            currentItemHovering = true
                            openPreviewTimer.start()
                        } else {
                            // Only reset if this was the current item being hovered
                            if (curItem === cameraItem.itemId) {
                                curItem = ""
                                currentItemHovering = false
                                hoverTimer.start()
                            }
                        }
                    }
                }

            }
        }
        MapItemView {
            model: thisMapModel.buildingIds
            delegate: MapItem {
                id: buildingItem
                model: modelData
                itemType: "BuildingItem"
                itemId: modelData.id
                itemName: modelData.name
                coordinate: QtPositioning.coordinate(modelData.latitude, modelData.longitude)
                onUntrackSignal: (buildingModel) => {
                    thisMapModel.removeBuildingFromMap(buildingModel)
                }
                onButtonSignal: () => {
                    if (thisMapState.editMode){
                        handleShowDialogEditMode(buildingItem, previewItemComponent)
                    }
                }
                Component {
                    id: previewItemComponent
                    PreviewItem {
                        id: previewItem
                        model: buildingItem.model
                        buttonType: "Building"
                        isViewMode: thisMapState ? !thisMapState.editMode : false
                        visible: true
                        itemName: buildingItem.itemName
                        onCloseSignal: {
                            cameraDetailLoader.sourceComponent = null;
                            cameraDetailLoader.width = previewWidth
                            cameraDetailLoader.height = previewHeight
                            thisMapState.viewMode = false
                        }
                        onFullScreenSignal: {
                            if (thisMapState.editMode){
                                handleDialogPositionEditMode(buildingItem)
                            }
                        }
                        Component.onCompleted:{
                            // Component initialization
                        }

                        onHoverStateChanged: function (isHovering) {
                            previewItemHovering = isHovering
                            if(!isHovering){
                                hoverTimer.start()
                            }
                            else hoverTimer.stop()
                        }
                    }
                }

                onIsItemHoveredChanged: {
                    if(!thisMapState.editMode){
                        openPreviewTimer.stop()
                        hoverTimer.stop()

                        if (isItemHovered) {
                            handlePositionDialogViewMode(buildingItem, previewItemComponent)
                            curItem = buildingItem.itemId
                            currentItemHovering = true
                            openPreviewTimer.start()
                        } else {
                            // Only reset if this was the current item being hovered
                            if (curItem === buildingItem.itemId) {
                                curItem = ""
                                currentItemHovering = false
                                hoverTimer.start()
                            }
                        }
                    }
                }
            }
        }

        Timer {
            id: hoverTimer
            interval: 200
            repeat: false
            onTriggered: {
                // console.log("hoverTimer triggered", previewItemHovering, currentItemHovering, thisMapState.editMode)
                if (!previewItemHovering && !currentItemHovering && !thisMapState.editMode) {
                    cameraDetailLoader.active = false
                    cameraDetailLoader.sourceComponent = null
                }
            }
        }

        Timer {
            id: openPreviewTimer
            interval: 1000
            repeat: false
            onTriggered: {
                if(!thisMapState.editMode){
                    cameraDetailLoader.active = true;
                }
            }
        }
    

        Behavior on center {
            PropertyAnimation {
                duration: 400
                easing.type: Easing.InOutQuad
            }
        }

        PinchHandler {
            id: pinch
            enabled: thisMapState ? !thisMapState.lockMode : false
            target: null
            onActiveChanged: if (active) {
                map.startCentroid = map.toCoordinate(pinch.centroid.position, false)
            }
            onScaleChanged: (delta) => {
                map.zoomLevel += Math.log2(delta)
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position)
            }
            onRotationChanged: (delta) => {
                map.bearing -= delta
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position)
            }
            grabPermissions: PointerHandler.TakeOverForbidden
        }
        WheelHandler {
            id: wheel
            enabled: thisMapState ? !thisMapState.lockMode : false
            grabPermissions: PointerHandler.ApprovesCancellation
            acceptedDevices: Qt.platform.pluginName === "cocoa" || Qt.platform.pluginName === "wayland"
                             ? PointerDevice.Mouse | PointerDevice.TouchPad
                             : PointerDevice.Mouse
            rotationScale: 1 /120
            property: "zoomLevel"
        }
        DragHandler {
            id: drag
            target: null
            grabPermissions: PointerHandler.TakeOverForbidden
            enabled: mapOnGrid.isMapMoveEnabled
            onTranslationChanged: (delta) => map.pan(-delta.x, -delta.y)
            // Make sure this handler doesn't interfere with hover events
            acceptedDevices: PointerDevice.Mouse | PointerDevice.TouchPad
        }


        Shortcut {
            enabled: map.zoomLevel < map.maximumZoomLevel
            sequence: StandardKey.ZoomIn
            onActivated: map.zoomLevel = Math.round(map.zoomLevel + 1)
        }
        Shortcut {
            enabled: map.zoomLevel > map.minimumZoomLevel
            sequence: StandardKey.ZoomOut
            onActivated: map.zoomLevel = Math.round(map.zoomLevel - 1)
        }
    }

    // Loader{
    //     id: searchBarLoader
    //     anchors.left: mapOnGrid.left
    //     anchors.top: mapOnGrid.top
    //     sourceComponent: Column {
    //         spacing: 4

    //         TextField {
    //             id: inputField
    //             width: 200
    //             placeholderText: qsTr("Search on map")
    //             onTextChanged: {
    //                 suggestionList.visible = text.length > 0
    //             }
    //         }

    //         ListView {
    //             id: suggestionList
    //             width: inputField.width
    //             height: 100
    //             visible: true
    //             model: ["apple", "banana", "orange", "mango"]
    //             delegate: ItemDelegate {
    //                 text: modelData
    //                 onClicked: {
    //                     inputField.text = modelData
    //                     suggestionList.visible = false
    //                 }
    //             }
    //         }
    //     }
    // }

    // DragHandler {
    //     id: dragHandler
    //     enabled: (thisMapState && thisMapState.lockMode) ? true : false
    //     onActiveChanged: {
    //         if (active) {
    //             root.currentMimeData = {
    //                 "text/plain": "swap_item",
    //                 "application/position": JSON.stringify({
    //                     id: mapModel.id,
    //                     tree_type: "",
    //                     position: widget.getPosition()
    //                 })
    //             }
    //         }
    //     }
    // }


    // Drag.source: root
    // Drag.active: dragHandler.active
    // Drag.mimeData: root.currentMimeData
    // // Drag.mimeData: {
    // //     "text/plain": "swap_item",
    // //     "application/position": JSON.stringify({
    // //         id: mapModel.id,
    // //         tree_type: "",
    // //         position: widget.getPosition()
    // //     })
    // // }

    // Drag.dragType: Drag.Automatic
    // Drag.supportedActions: Qt.MoveAction

    Connections {
        target: thisMapModel
        function onNewCameraChanged(camera) {
            cameraList.push(camera)
        }

        
    }
    Connections {
        target: thisMapState
        function onLockModeChanged(){
            if(thisMapState.lockMode){
                cameraDetailLoader.sourceComponent = null;
            }
        }
    }

    // Add function to handle map movement from parent
    function handleMapMove(dx, dy) {
        map.handleMapMove(dx, dy)
    }

    // Add property to control map movement mode
    property bool isMapMoveEnabled: true

    // Add signal to notify parent of map movement mode changes
    signal mapMoveModeChanged(bool enabled)

    // Update map movement mode
    function setMapMoveMode(enabled) {
        isMapMoveEnabled = enabled
        // Ensure the map's drag handler is properly updated
        drag.enabled = enabled
        // Also update the map's internal property
        map.isMapMoveEnabled = enabled
        mapMoveModeChanged(enabled)
    }

    function handleDialogPositionEditMode(item){
        thisMapState.viewMode = !thisMapState.viewMode
        if (thisMapState.viewMode){
            cameraDetailLoader.width = map.width
            cameraDetailLoader.height = map.height
            cameraDetailLoader.x = 0
            cameraDetailLoader.y = 0
        }else{
            cameraDetailLoader.width = previewWidth
            cameraDetailLoader.height = previewHeight
            if (item.x < 0) {
                cameraDetailLoader.x = 0
            }else{
                if ((item.x + previewWidth + 30) > map.width){
                    cameraDetailLoader.x = map.width - previewWidth - 30
                }else{
                    cameraDetailLoader.x = item.x + 30;
                }
            }
            if (item.y < 0) {
                cameraDetailLoader.y = 0
            }else {
                if ((item.y + previewHeight + 30) > map.height){
                    cameraDetailLoader.y = map.height - previewHeight - 30
                }else{
                    cameraDetailLoader.y = item.y + 30;
                }
            }
        }
    }

    function handleShowDialogEditMode(item, previewItemComponent){
        if (item.x < 0) {
            cameraDetailLoader.x = 0
        }else{
            if ((item.x + previewWidth + 30) > map.width){
                cameraDetailLoader.x = map.width - previewWidth - 30
            }else{
                cameraDetailLoader.x = item.x + 30;
            }
        }
        if (item.y < 0) {
            cameraDetailLoader.y = 0
        }else {
            if ((item.y + previewHeight + 30) > map.height){
                cameraDetailLoader.y = map.height - previewHeight - 30
            }else{
                cameraDetailLoader.y = item.y + 30;
            }
        }
        cameraDetailLoader.sourceComponent = previewItemComponent
    }

    function handlePositionDialogViewMode(item, previewItemComponent){
        cameraDetailLoader.active = false;
        var defaultMapX = item.x + (item.width/2) - (previewWidth/2);
        var defaultMapY = item.y + item.height + 10;

        var pt = mapOnGrid.mapToItem(mainGrid, Qt.point(defaultMapX, defaultMapY));

        var clampedX = Math.max(0, Math.min(pt.x, mainGrid.width - previewWidth));
        var clampedY = Math.max(0, Math.min(pt.y, mainGrid.height - previewHeight));

        cameraDetailLoader.x = clampedX;
        cameraDetailLoader.y = clampedY;
        cameraDetailLoader.sourceComponent = previewItemComponent;
    }
}
